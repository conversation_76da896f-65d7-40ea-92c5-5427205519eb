import { Component, OnInit, ElementRef, ViewChild, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import * as THREE from 'three';
import { AbstractThing, BoxThing, RectangleThing, SphereThing, TriangleThing } from './things';

export interface ThingData {
  type: string;
  name: string;
  status: 'on' | 'off' | string;
  pos: [number, number, number];
  width?: number;
  height?: number;
  depth?: number;
  radius?: number;
  base?: number;
  widthSegments?: number;
  heightSegments?: number;
}

@Component({
  selector: 'app-three',
  standalone: true,
  templateUrl: './three.component.html',
  styleUrls: ['./three.component.scss']
})
export class ThreeComponent implements OnInit, OnDestroy {
  @ViewChild('rendererContainer', { static: true }) rendererContainer!: ElementRef;

  private scene!: THREE.Scene;
  private camera!: THREE.OrthographicCamera;
  private renderer!: THREE.WebGLRenderer;
  private animationId!: number;
  private clock = new THREE.Clock();

  private robotPivot!: THREE.Object3D; // spostamento corpo
  private headPivot!: THREE.Object3D;  // rotazione testa (mouse)

  private movementState = {
    forward: false,
    backward: false,
    left: false,
    right: false,
    up: false,
    down: false,
  };

  // per mouse look
  private yaw = 0;   // rotazione orizzontale testa target
  private pitch = 0; // rotazione verticale testa target
  private readonly pitchLimit = Math.PI / 3; // ±60°
  private readonly yawSpeed = 0.0025;
  private readonly pitchSpeed = 0.0025;

  // smoothing facoltativo (disabilitato)
  private currentYaw = 0;
  private currentPitch = 0;
  private readonly smoothingEnabled = false;
  private readonly smoothingFactor = 0.1;

  // drag-based look state
  private isDragging = false;
  private prevMouse: { x: number; y: number } | null = null;

  // raycast point-and-click
  private raycaster = new THREE.Raycaster();
  private pointer = new THREE.Vector2();
  private selectedThingMesh: THREE.Object3D | null = null;
  private highlightOutline: THREE.LineSegments | null = null;
  private selectedObjectPosition: THREE.Vector3 | null = null;

  // camera animation for smooth transitions
  private isAnimatingCamera = false;
  private animationStartTime = 0;
  private animationDuration = 1000; // 1 second
  private startCameraPosition = new THREE.Vector3();
  private targetCameraPosition = new THREE.Vector3();
  private startCameraRotation = new THREE.Euler();
  private targetCameraRotation = new THREE.Euler();

  // scena di esempio
  private jsonScene: ThingData[] = [
    {
      type: 'box',
      name: 'Rack-1-Server-A',
      status: 'on',
      pos: [0, 0.5, 0],
      width: 1,
      height: 1,
      depth: 1
    },
    {
      type: 'sphere',
      name: 'Rack-1-Server-B',
      status: 'off',
      pos: [4, 0.5, 0],
      radius: 0.5,
      widthSegments: 16,
      heightSegments: 12
    },
    {
      type: 'triangle',
      name: 'Rack-2-Server-A',
      status: 'on',
      pos: [0, 0.5, -2],
      base: 1.5,
      height: 1
    },
    {
      type: 'rectangle',
      name: 'Rack-2-Server-B',
      status: 'on',
      pos: [4, 0.5, -2],
      width: 2,
      height: 1,
      depth: 0.2
    }
  ];

  ngOnInit() {
    this.initThree();
    this.loadJsonScene({ things: this.jsonScene });
    this.animate();
  }

  ngOnDestroy() {
    cancelAnimationFrame(this.animationId);
    this.renderer.dispose();

    // rimozione listener
    window.removeEventListener('keydown', this.onKeyDown);
    window.removeEventListener('keyup', this.onKeyUp);
    window.removeEventListener('resize', this.onResize);
    this.renderer.domElement.removeEventListener('mousedown', this.onMouseDown);
    this.renderer.domElement.removeEventListener('mouseup', this.onMouseUp);
    this.renderer.domElement.removeEventListener('mousemove', this.onMouseMove);
    this.renderer.domElement.removeEventListener('click', this.onClick);
    this.renderer.domElement.removeEventListener('dragstart', this.preventDrag);
  }

  private initThree() {
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0xf0f0f0);

    // Renderer
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(
      this.rendererContainer.nativeElement.clientWidth,
      this.rendererContainer.nativeElement.clientHeight
    );
    this.rendererContainer.nativeElement.appendChild(this.renderer.domElement);

    // Camera & pivots - Isometric setup
    const aspect = this.rendererContainer.nativeElement.clientWidth /
      this.rendererContainer.nativeElement.clientHeight;
    const frustumSize = 20;
    this.camera = new THREE.OrthographicCamera(
      (frustumSize * aspect) / -2,
      (frustumSize * aspect) / 2,
      frustumSize / 2,
      frustumSize / -2,
      0.1,
      1000
    );

    this.robotPivot = new THREE.Object3D();
    this.scene.add(this.robotPivot);

    this.headPivot = new THREE.Object3D();
    this.robotPivot.add(this.headPivot);

    // Posizionamento camera per vista isometrica
    this.camera.position.set(10, 10, 10);
    this.headPivot.add(this.camera);

    // Set initial camera angle for isometric view
    this.pitch = -Math.PI / 4; // -45 degrees looking down
    this.yaw = Math.PI / 4; // 45 degrees rotation for isometric angle
    this.currentPitch = this.pitch;
    this.currentYaw = this.yaw;
    this.updateCameraRotation();

    // Luci
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8);
    this.scene.add(ambientLight);
    const dirLight = new THREE.DirectionalLight(0xffffff, 0.5);
    dirLight.position.set(5, 10, 7.5);
    this.scene.add(dirLight);

    // Ground plane
    const groundGeometry = new THREE.PlaneGeometry(50, 50);
    const groundMaterial = new THREE.MeshLambertMaterial({
      color: 0x888888,
      transparent: true,
      opacity: 0.8
    });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2; // Rotate to be horizontal
    ground.position.y = -0.01; // Slightly below y=0 to avoid z-fighting with grid
    this.scene.add(ground);

    // Assi e griglia
    // this.scene.add(new THREE.AxesHelper(5));
    this.scene.add(new THREE.GridHelper(20, 20));

    // Input
    this.setupKeyboardMovement();
    this.setupMouseLook();
    this.setupPointerSelection();

    // Resize
    window.addEventListener('resize', this.onResize);
  }

  // resize handler separato per rimozione pulita
  private onResize = () => {
    const aspect = this.rendererContainer.nativeElement.clientWidth /
      this.rendererContainer.nativeElement.clientHeight;
    const frustumSize = 20;
    this.camera.left = (frustumSize * aspect) / -2;
    this.camera.right = (frustumSize * aspect) / 2;
    this.camera.top = frustumSize / 2;
    this.camera.bottom = frustumSize / -2;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(
      this.rendererContainer.nativeElement.clientWidth,
      this.rendererContainer.nativeElement.clientHeight
    );
  };

  private onKeyDown = (e: KeyboardEvent) => {
    switch (e.key.toLowerCase()) {
      case 'w':
        this.movementState.forward = true;
        break;
      case 's':
        this.movementState.backward = true;
        break;
      case 'a':
        this.movementState.left = true;  // strafe left
        break;
      case 'd':
        this.movementState.right = true; // strafe right
        break;
      case 'q':
        this.movementState.up = true;    // sale in altezza
        break;
      case 'e':
        this.movementState.down = true;  // scende in altezza
        break;
    }
  };

  private onKeyUp = (e: KeyboardEvent) => {
    switch (e.key.toLowerCase()) {
      case 'w':
        this.movementState.forward = false;
        break;
      case 's':
        this.movementState.backward = false;
        break;
      case 'a':
        this.movementState.left = false;
        break;
      case 'd':
        this.movementState.right = false;
        break;
      case 'q':
        this.movementState.up = false;
        break;
      case 'e':
        this.movementState.down = false;
        break;
    }
  };

  private setupKeyboardMovement() {
    window.addEventListener('keydown', this.onKeyDown);
    window.addEventListener('keyup', this.onKeyUp);
  }

  private setupMouseLook() {
    this.renderer.domElement.addEventListener('mousedown', this.onMouseDown);
    this.renderer.domElement.addEventListener('mouseup', this.onMouseUp);
    this.renderer.domElement.addEventListener('mousemove', this.onMouseMove);
    this.renderer.domElement.addEventListener('dragstart', this.preventDrag);
  }

  private setupPointerSelection() {
    this.renderer.domElement.addEventListener('click', this.onClick);
  }

  private preventDrag = (e: Event) => e.preventDefault();

  // ---- DRAG-BASED MOUSE LOOK ----

  private onMouseDown = (e: MouseEvent) => {
    if (e.button !== 0 || this.isAnimatingCamera) return; // solo sinistro e non durante animazione
    this.isDragging = true;
    this.prevMouse = { x: e.clientX, y: e.clientY };
  };

  private onMouseUp = (e: MouseEvent) => {
    if (e.button !== 0) return;
    this.isDragging = false;
    this.prevMouse = null;
  };

  private onMouseMove = (e: MouseEvent) => {
    if (!this.isDragging || !this.prevMouse || this.isAnimatingCamera) return;

    const dx = e.clientX - this.prevMouse.x;
    const dy = e.clientY - this.prevMouse.y;
    this.prevMouse = { x: e.clientX, y: e.clientY };

    this.yaw -= dx * this.yawSpeed;
    this.pitch -= dy * this.pitchSpeed;

    // clamp pitch
    this.pitch = Math.max(-this.pitchLimit, Math.min(this.pitchLimit, this.pitch));

    // smoothing opzionale
    if (this.smoothingEnabled) {
      this.currentYaw += (this.yaw - this.currentYaw) * this.smoothingFactor;
      this.currentPitch += (this.pitch - this.currentPitch) * this.smoothingFactor;
    } else {
      this.currentYaw = this.yaw;
      this.currentPitch = this.pitch;
    }

    this.updateCameraRotation();
  };

  private updateCameraRotation() {
    if (this.selectedObjectPosition) {
      // Rotate around selected object
      this.rotateAroundSelectedObject();
    } else {
      // Default rotation behavior (around robot pivot)
      const quatYaw = new THREE.Quaternion().setFromAxisAngle(new THREE.Vector3(0, 1, 0), this.currentYaw);
      const quatPitch = new THREE.Quaternion().setFromAxisAngle(new THREE.Vector3(1, 0, 0), this.currentPitch);
      this.headPivot.quaternion.copy(quatYaw).multiply(quatPitch);
    }
  }

  private rotateAroundSelectedObject() {
    if (!this.selectedObjectPosition) return;

    // Distance from camera to selected object
    const distance = 15; // Fixed distance for isometric view

    // Calculate new camera position based on yaw and pitch
    const x = this.selectedObjectPosition.x + distance * Math.cos(this.currentPitch) * Math.sin(this.currentYaw);
    const y = this.selectedObjectPosition.y + distance * Math.sin(this.currentPitch);
    const z = this.selectedObjectPosition.z + distance * Math.cos(this.currentPitch) * Math.cos(this.currentYaw);

    // Update robot pivot position to place camera at calculated position
    this.robotPivot.position.set(x, y, z);

    // Make camera look at the selected object
    this.camera.lookAt(this.selectedObjectPosition);

    // Reset head pivot rotation since we're using lookAt
    this.headPivot.quaternion.set(0, 0, 0, 1);
  }

  private initializeOrbitCamera() {
    if (!this.selectedObjectPosition) return;

    // Calculate current camera world position
    const currentCameraPos = new THREE.Vector3();
    this.camera.getWorldPosition(currentCameraPos);

    // Calculate the vector from selected object to current camera position
    const offsetVector = new THREE.Vector3().subVectors(currentCameraPos, this.selectedObjectPosition);
    const distance = offsetVector.length();

    // If camera is too close, set a minimum distance
    const minDistance = 10;
    const finalDistance = Math.max(distance, minDistance);

    // Normalize the offset vector and scale to desired distance
    offsetVector.normalize().multiplyScalar(finalDistance);

    // Calculate yaw and pitch from the offset vector
    this.currentYaw = Math.atan2(offsetVector.x, offsetVector.z);
    this.currentPitch = Math.asin(offsetVector.y / finalDistance);

    // Update target values to match current values
    this.yaw = this.currentYaw;
    this.pitch = this.currentPitch;

    // Position the camera
    this.robotPivot.position.copy(this.selectedObjectPosition).add(offsetVector);
    this.camera.lookAt(this.selectedObjectPosition);
    this.headPivot.quaternion.set(0, 0, 0, 1);
  }

  // ---- POINT-AND-CLICK SELECTION ----

  private onClick = (e: MouseEvent) => {
    const rect = this.renderer.domElement.getBoundingClientRect();
    this.pointer.x = ((e.clientX - rect.left) / rect.width) * 2 - 1;
    this.pointer.y = -((e.clientY - rect.top) / rect.height) * 2 + 1;

    this.raycaster.setFromCamera(this.pointer, this.camera);

    // Filter out non-selectable objects from raycast targets
    const selectableObjects = this.scene.children.filter(child =>
      child !== this.highlightOutline &&
      !(child instanceof THREE.Sprite) &&
      !(child instanceof THREE.GridHelper) &&
      !(child instanceof THREE.AxesHelper) &&
      !(child instanceof THREE.DirectionalLight) &&
      !(child instanceof THREE.AmbientLight)
    );
    
    const intersects = this.raycaster.intersectObjects(selectableObjects, true);

    if (intersects.length > 0) {
      const hit = intersects[0].object;

      // Deselect precedente
      this.clearHighlight();

      // Selezione corrente
      this.selectedThingMesh = hit;

      // Evidenzia con outline wireframe box
      const box = new THREE.Box3().setFromObject(hit);
      const size = new THREE.Vector3();
      const center = new THREE.Vector3();
      box.getSize(size);
      box.getCenter(center);

      // Store selected object position for rotation pivot
      this.selectedObjectPosition = center.clone();

      // Immediately position camera around the selected object
      this.updateCameraRotation();

      const geometry = new THREE.BoxGeometry(
        Math.max(size.x, 0.001) * 1.05,
        Math.max(size.y, 0.001) * 1.05,
        Math.max(size.z, 0.001) * 1.05
      );
      const edges = new THREE.EdgesGeometry(geometry);
      const outline = new THREE.LineSegments(
        edges,
        new THREE.LineBasicMaterial({ color: 0xff0000 })
      );
      outline.position.copy(center);
      this.highlightOutline = outline;
      this.scene.add(outline);

      // Mostra nome come sprite sopra (se presente)
      if (hit.name) {
        const sprite = ThreeComponent.createTextSprite(hit.name);
        sprite.position.copy(center);
        sprite.position.y += size.y + 0.2;
        this.scene.add(sprite);
        setTimeout(() => this.scene.remove(sprite), 2500);
      }

      console.log('Clicked on thing:', hit.name || hit.uuid);

      // Don't animate camera when we're now rotating around the object
      // The rotation system will handle positioning automatically
    } else {
      this.clearHighlight();
      this.selectedThingMesh = null;
      this.selectedObjectPosition = null;
    }
  };

  private clearHighlight() {
    if (this.highlightOutline) {
      this.scene.remove(this.highlightOutline);
      this.highlightOutline.geometry.dispose();
      (this.highlightOutline.material as THREE.Material).dispose();
      this.highlightOutline = null;
    }
    this.selectedObjectPosition = null;
  }





private applyKeyboardMovement(delta: number) {
  const moveSpeed = 5 * delta;

  // Ottieni direzione "forward" della camera, proiettata sul piano XZ (y = 0)
  const camDir = new THREE.Vector3();
  this.camera.getWorldDirection(camDir); // include pitch
  camDir.y = 0;
  camDir.normalize();

  // Direzione right come ortogonale in XZ
  const camRight = new THREE.Vector3();
  camRight.crossVectors(camDir, new THREE.Vector3(0, 1, 0)).normalize();

  if (this.movementState.forward) {
    this.robotPivot.position.addScaledVector(camDir, moveSpeed);
  }
  if (this.movementState.backward) {
    this.robotPivot.position.addScaledVector(camDir, -moveSpeed);
  }
  if (this.movementState.left) {
    this.robotPivot.position.addScaledVector(camRight, -moveSpeed);
  }
  if (this.movementState.right) {
    this.robotPivot.position.addScaledVector(camRight, moveSpeed);
  }
  if (this.movementState.up) {
    this.robotPivot.position.y += moveSpeed;
  }
  if (this.movementState.down) {
    this.robotPivot.position.y -= moveSpeed;
  }
}

  private animate = () => {
    this.animationId = requestAnimationFrame(this.animate);
    const delta = this.clock.getDelta(); // delta reale

    // Handle camera animation
    this.updateCameraAnimation();

    // Only apply keyboard movement if not animating camera
    if (!this.isAnimatingCamera) {
      this.applyKeyboardMovement(delta);
    }

    this.renderer.render(this.scene, this.camera);
  };

  private updateCameraAnimation() {
    if (!this.isAnimatingCamera) return;

    const currentTime = performance.now();
    const elapsed = currentTime - this.animationStartTime;
    const progress = Math.min(elapsed / this.animationDuration, 1);

    // Use easing function for smooth animation (ease-in-out)
    const easedProgress = this.easeInOutCubic(progress);

    // Only animate position if we're not rotating around a selected object
    if (!this.selectedObjectPosition) {
      // Interpolate position
      this.robotPivot.position.lerpVectors(
        this.startCameraPosition,
        this.targetCameraPosition,
        easedProgress
      );

      // Only interpolate rotation if target rotation is different from start rotation
      const rotationChanged =
        Math.abs(this.startCameraRotation.y - this.targetCameraRotation.y) > 0.001 ||
        Math.abs(this.startCameraRotation.x - this.targetCameraRotation.x) > 0.001;

      if (rotationChanged) {
        this.currentYaw = this.lerp(
          this.startCameraRotation.y,
          this.targetCameraRotation.y,
          easedProgress
        );
        this.currentPitch = this.lerp(
          this.startCameraRotation.x,
          this.targetCameraRotation.x,
          easedProgress
        );

        // Update camera rotation
        this.updateCameraRotation();
      }
    }

    // Check if animation is complete
    if (progress >= 1) {
      this.isAnimatingCamera = false;
      // Only update target values if we're not rotating around a selected object
      if (!this.selectedObjectPosition) {
        this.yaw = this.currentYaw;
        this.pitch = this.currentPitch;
      }
    }
  }

  private easeInOutCubic(t: number): number {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  }

  private lerp(start: number, end: number, t: number): number {
    return start + (end - start) * t;
  }

  private loadJsonScene(data: { things: ThingData[] }) {
    data.things.forEach((obj) => {
      let thing: AbstractThing | null = null;
      switch (obj.type.toLowerCase()) {
        case 'box':
          thing = new BoxThing(obj);
          break;
        case 'sphere':
          thing = new SphereThing(obj);
          break;
        case 'triangle':
          thing = new TriangleThing(obj);
          break;
        case 'rectangle':
          thing = new RectangleThing(obj);
          break;
        default:
          console.warn(`Unknown thing type: ${obj.type}`);
      }
      if (thing) {
        const mesh = thing.getObject();
        mesh.position.set(obj.pos[0], obj.pos[1], obj.pos[2]);
        mesh.name = obj.name;
        mesh.userData['thingData'] = obj;
        this.scene.add(mesh);
      }
    });
  }

  static createTextSprite(text: string): THREE.Sprite {
    const canvas = document.createElement('canvas');
    canvas.width = 512;
    canvas.height = 128;
    const context = canvas.getContext('2d')!;
    context.font = '48px Arial';
    context.fillStyle = 'black';
    context.fillText(text, 10, 64);
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;
    const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
    const sprite = new THREE.Sprite(spriteMaterial);
    sprite.scale.set(3, 1, 1);
    return sprite;
  }
}
